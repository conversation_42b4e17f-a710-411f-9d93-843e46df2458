import ctypes
import ctypes.wintypes
import os
import logging
import winreg
from pathlib import Path
from typing import Optional
from enum import Enum

class WallpaperStyle(Enum):
    """壁纸样式枚举"""
    CENTER = ("0", "0")    # 居中
    TILE = ("0", "1")      # 平铺
    STRETCH = ("2", "0")   # 拉伸
    FIT = ("6", "0")       # 适应
    FILL = ("10", "0")     # 填充
    SPAN = ("22", "0")     # 跨越

    def __init__(self, style, tile):
        self.style = style
        self.tile = tile

class WindowsWallpaperSetter:
    """Windows壁纸设置器"""
    
    # Windows API常量
    SPI_SETDESKWALLPAPER = 0x0014
    SPIF_UPDATEINIFILE = 0x0001
    SPIF_SENDCHANGE = 0x0002
    
    def __init__(self, default_style: WallpaperStyle = WallpaperStyle.FILL):
        self.default_style = default_style
        
        # 检查是否为Windows系统
        if not self.is_windows():
            raise OSError("This wallpaper setter only works on Windows")
        
        logging.info(f"Windows wallpaper setter initialized with style: {default_style.name}")
    
    @staticmethod
    def is_windows() -> bool:
        """检查是否为Windows系统"""
        return os.name == 'nt'
    
    def set_wallpaper(self, image_path: Path, style: Optional[WallpaperStyle] = None) -> bool:
        """设置桌面壁纸"""
        if style is None:
            style = self.default_style
        
        if not image_path.exists():
            logging.error(f"Wallpaper file does not exist: {image_path}")
            return False
        
        # 转换为绝对路径
        abs_path = image_path.resolve()
        path_str = str(abs_path)
        
        try:
            logging.info(f"Setting wallpaper: {path_str} with style: {style.name}")
            
            # 设置壁纸样式
            self._set_wallpaper_style(style)
            
            # 调用Windows API设置壁纸
            result = ctypes.windll.user32.SystemParametersInfoW(
                self.SPI_SETDESKWALLPAPER,
                0,
                path_str,
                self.SPIF_UPDATEINIFILE | self.SPIF_SENDCHANGE
            )
            
            if result:
                logging.info("Successfully set wallpaper")
                # 保存当前壁纸路径到注册表
                self._save_current_wallpaper_path(path_str)
                return True
            else:
                error_code = ctypes.windll.kernel32.GetLastError()
                logging.error(f"Failed to set wallpaper, error code: {error_code}")
                return False
                
        except Exception as e:
            logging.error(f"Error setting wallpaper: {e}")
            return False
    
    def _set_wallpaper_style(self, style: WallpaperStyle):
        """设置壁纸样式到注册表"""
        try:
            registry_path = r"Control Panel\\Desktop"
            
            # 打开注册表项
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, registry_path, 0, winreg.KEY_SET_VALUE) as key:
                # 设置壁纸样式
                winreg.SetValueEx(key, "WallpaperStyle", 0, winreg.REG_SZ, style.style)
                winreg.SetValueEx(key, "TileWallpaper", 0, winreg.REG_SZ, style.tile)
            
            logging.debug(f"Set wallpaper style in registry: {style.name}")
            
        except Exception as e:
            logging.error(f"Failed to set wallpaper style: {e}")
    
    def _save_current_wallpaper_path(self, path: str):
        """保存当前壁纸路径到注册表"""
        try:
            registry_path = r"Control Panel\\Desktop"
            
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, registry_path, 0, winreg.KEY_SET_VALUE) as key:
                winreg.SetValueEx(key, "Wallpaper", 0, winreg.REG_SZ, path)
                
        except Exception as e:
            logging.error(f"Failed to save wallpaper path to registry: {e}")
    
    def get_current_wallpaper_path(self) -> Optional[str]:
        """获取当前设置的壁纸路径"""
        try:
            registry_path = r"Control Panel\\Desktop"
            
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, registry_path, 0, winreg.KEY_READ) as key:
                path, _ = winreg.QueryValueEx(key, "Wallpaper")
                return path
                
        except Exception as e:
            logging.error(f"Failed to get current wallpaper path: {e}")
            return None
    
    def get_current_wallpaper_style(self) -> Optional[WallpaperStyle]:
        """获取当前壁纸样式"""
        try:
            registry_path = r"Control Panel\\Desktop"
            
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, registry_path, 0, winreg.KEY_READ) as key:
                style_value, _ = winreg.QueryValueEx(key, "WallpaperStyle")
                tile_value, _ = winreg.QueryValueEx(key, "TileWallpaper")
                
                # 匹配壁纸样式
                for style in WallpaperStyle:
                    if style.style == style_value and style.tile == tile_value:
                        return style
                        
                logging.warning(f"Unknown wallpaper style: {style_value}, {tile_value}")
                return None
                
        except Exception as e:
            logging.error(f"Failed to get current wallpaper style: {e}")
            return None
    
    def test_wallpaper_setting(self) -> bool:
        """测试壁纸设置功能"""
        try:
            # 获取当前壁纸路径作为测试
            current_path = self.get_current_wallpaper_path()
            
            if current_path and Path(current_path).exists():
                # 尝试重新设置当前壁纸
                result = self.set_wallpaper(Path(current_path))
                logging.info(f"Wallpaper setting test {'passed' if result else 'failed'}")
                return result
            else:
                logging.warning("No current wallpaper found for testing")
                return False
                
        except Exception as e:
            logging.error(f"Wallpaper setting test failed: {e}")
            return False
    
    def get_supported_formats(self) -> list:
        """获取支持的图片格式"""
        return ['.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff', '.webp']
    
    def is_supported_format(self, file_path: Path) -> bool:
        """检查文件格式是否受支持"""
        return file_path.suffix.lower() in self.get_supported_formats()

def get_wallpaper_style_by_name(style_name: str) -> WallpaperStyle:
    """根据名称获取壁纸样式"""
    style_name = style_name.upper()
    
    style_mapping = {
        'CENTER': WallpaperStyle.CENTER,
        'TILE': WallpaperStyle.TILE,
        'STRETCH': WallpaperStyle.STRETCH,
        'FIT': WallpaperStyle.FIT,
        'FILL': WallpaperStyle.FILL,
        'SPAN': WallpaperStyle.SPAN
    }
    
    return style_mapping.get(style_name, WallpaperStyle.FILL)

# 测试函数
def test_wallpaper_setter():
    """测试壁纸设置器"""
    try:
        setter = WindowsWallpaperSetter()
        
        print("Testing wallpaper setter...")
        print(f"Current wallpaper: {setter.get_current_wallpaper_path()}")
        print(f"Current style: {setter.get_current_wallpaper_style()}")
        print(f"Test result: {setter.test_wallpaper_setting()}")
        print(f"Supported formats: {setter.get_supported_formats()}")
        
    except Exception as e:
        print(f"Test failed: {e}")

if __name__ == "__main__":
    # 设置基本日志
    logging.basicConfig(level=logging.INFO)
    test_wallpaper_setter()