# Wallpaper Updater

基于Java的自动壁纸更新程序，使用Wallhaven API获取高质量壁纸并自动设置为Windows桌面背景。

## 功能特性

- 从Wallhaven API获取高质量壁纸
- 支持按分类、纯度、分辨率等条件筛选
- 自动下载和本地缓存管理
- 定时自动更新壁纸
- 支持多种壁纸适配模式（填充、适应、拉伸等）
- 启动时立即更新选项
- 交互式命令行界面

## 系统要求

- Windows操作系统
- Java 11或更高版本
- Maven 3.6+（用于构建）

## 快速开始

1. **获取Wallhaven API密钥**（可选但推荐）
   - 访问 https://wallhaven.cc 并注册账号
   - 进入设置页面获取API密钥

2. **配置程序**
   - 编辑 `src/main/resources/config.properties`
   - 填入你的API密钥：`wallhaven.apiKey=your-api-key-here`
   - 根据需要调整其他配置

3. **构建程序**
   ```bash
   cd wallpaper-updater
   mvn clean package
   ```

4. **运行程序**
   ```bash
   java -jar target/wallpaper-updater-1.0.0-jar-with-dependencies.jar
   ```

## 配置说明

### config.properties

```properties
# Wallhaven API配置
wallhaven.apiKey=              # 你的API密钥
wallhaven.categories=general    # 分类：general,anime,people
wallhaven.purity=sfw           # 纯度：sfw,sketchy,nsfw
wallhaven.sorting=toplist      # 排序：toplist,random,views,favorites
wallhaven.topRange=1M          # 时间范围：1d,3d,1w,1M,3M,6M,1y
wallhaven.minResolution=1920x1080  # 最小分辨率
wallhaven.ratios=16x9          # 宽高比

# 更新设置
update.schedule.cron=0 0 9 * * ?  # Cron表达式（每天9点）
update.onStartup=true          # 启动时更新
update.randomize=true          # 随机选择壁纸

# 存储设置
storage.path=./wallpapers      # 壁纸存储路径
storage.maxFiles=50            # 最大保留文件数
storage.cleanupOldFiles=true   # 自动清理旧文件

# Windows设置
windows.wallpaperStyle=fill    # 壁纸样式：center,tile,stretch,fit,fill,span
```

## 使用说明

程序启动后进入交互式命令行界面，支持以下命令：

- `update` 或 `u` - 立即更新壁纸
- `status` 或 `s` - 查看当前状态
- `help` 或 `h` - 显示帮助信息
- `quit` 或 `q` - 退出程序

## Cron表达式示例

- `0 0 * * * ?` - 每小时更新
- `0 0 9,15 * * ?` - 每天9点和15点更新
- `0 30 8 ? * MON-FRI` - 工作日8:30更新
- `0 0 12 * * ?` - 每天12点更新

## 注意事项

1. 首次运行会创建 `wallpapers` 目录用于存储下载的壁纸
2. 日志文件保存在 `logs` 目录下
3. 可以创建 `user-config.properties` 文件来覆盖默认配置
4. 确保有足够的磁盘空间存储壁纸文件

## 故障排除

1. **无法设置壁纸**
   - 确认运行在Windows系统上
   - 以管理员权限运行程序

2. **下载失败**
   - 检查网络连接
   - 验证API密钥是否正确
   - 查看日志文件了解详细错误信息

3. **定时任务不执行**
   - 检查Cron表达式是否正确
   - 确保程序持续运行

## 技术栈

- Java 11
- OkHttp - HTTP客户端
- Jackson - JSON处理
- JNA - Windows API调用
- Quartz - 定时任务
- Logback - 日志框架