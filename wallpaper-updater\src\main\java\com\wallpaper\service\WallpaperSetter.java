package com.wallpaper.service;

import com.sun.jna.Native;
import com.sun.jna.platform.win32.Advapi32Util;
import com.sun.jna.platform.win32.WinReg;
import com.sun.jna.win32.StdCallLibrary;
import com.sun.jna.win32.W32APIOptions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;

public class WallpaperSetter {
    private static final Logger logger = LoggerFactory.getLogger(WallpaperSetter.class);
    
    private static final int SPI_SETDESKWALLPAPER = 0x0014;
    private static final int SPIF_UPDATEINIFILE = 0x0001;
    private static final int SPIF_SENDCHANGE = 0x0002;
    
    public enum WallpaperStyle {
        CENTER("0", "0"),
        TILE("0", "1"),
        STRETCH("2", "0"),
        FIT("6", "0"),
        FILL("10", "0"),
        SPAN("22", "0");
        
        private final String style;
        private final String tile;
        
        WallpaperStyle(String style, String tile) {
            this.style = style;
            this.tile = tile;
        }
    }
    
    public interface User32 extends StdCallLibrary {
        User32 INSTANCE = Native.load("user32", User32.class, W32APIOptions.DEFAULT_OPTIONS);
        
        boolean SystemParametersInfo(int uiAction, int uiParam, String pvParam, int fWinIni);
    }
    
    private final WallpaperStyle defaultStyle;
    
    public WallpaperSetter(WallpaperStyle defaultStyle) {
        this.defaultStyle = defaultStyle;
    }
    
    public boolean setWallpaper(File wallpaperFile) {
        return setWallpaper(wallpaperFile, defaultStyle);
    }
    
    public boolean setWallpaper(File wallpaperFile, WallpaperStyle style) {
        if (!wallpaperFile.exists()) {
            logger.error("Wallpaper file does not exist: {}", wallpaperFile.getAbsolutePath());
            return false;
        }
        
        try {
            String wallpaperPath = wallpaperFile.getAbsolutePath();
            logger.info("Setting wallpaper: {} with style: {}", wallpaperPath, style);
            
            // Set wallpaper style
            setWallpaperStyle(style);
            
            // Set the wallpaper
            boolean result = User32.INSTANCE.SystemParametersInfo(
                    SPI_SETDESKWALLPAPER,
                    0,
                    wallpaperPath,
                    SPIF_UPDATEINIFILE | SPIF_SENDCHANGE
            );
            
            if (result) {
                logger.info("Successfully set wallpaper");
                // Save current wallpaper path to registry
                saveCurrentWallpaperPath(wallpaperPath);
            } else {
                logger.error("Failed to set wallpaper");
            }
            
            return result;
            
        } catch (Exception e) {
            logger.error("Error setting wallpaper", e);
            return false;
        }
    }
    
    private void setWallpaperStyle(WallpaperStyle style) {
        try {
            // Set wallpaper style in registry
            Advapi32Util.registrySetStringValue(
                    WinReg.HKEY_CURRENT_USER,
                    "Control Panel\\Desktop",
                    "WallpaperStyle",
                    style.style
            );
            
            Advapi32Util.registrySetStringValue(
                    WinReg.HKEY_CURRENT_USER,
                    "Control Panel\\Desktop",
                    "TileWallpaper",
                    style.tile
            );
            
            logger.debug("Set wallpaper style: {}", style);
            
        } catch (Exception e) {
            logger.error("Failed to set wallpaper style", e);
        }
    }
    
    private void saveCurrentWallpaperPath(String path) {
        try {
            Advapi32Util.registrySetStringValue(
                    WinReg.HKEY_CURRENT_USER,
                    "Control Panel\\Desktop",
                    "Wallpaper",
                    path
            );
        } catch (Exception e) {
            logger.error("Failed to save wallpaper path to registry", e);
        }
    }
    
    public String getCurrentWallpaperPath() {
        try {
            return Advapi32Util.registryGetStringValue(
                    WinReg.HKEY_CURRENT_USER,
                    "Control Panel\\Desktop",
                    "Wallpaper"
            );
        } catch (Exception e) {
            logger.error("Failed to get current wallpaper path", e);
            return null;
        }
    }
    
    public static boolean isWindowsOS() {
        String os = System.getProperty("os.name").toLowerCase();
        return os.contains("win");
    }
}