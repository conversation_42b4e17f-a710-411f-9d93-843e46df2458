import os
import sys
import logging
import colorama
from colorama import Fore, Style
from pathlib import Path
from datetime import datetime
from typing import Optional

# 初始化colorama（Windows颜色支持）
colorama.init()

class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器"""
    
    # 日志级别颜色映射
    COLORS = {
        'DEBUG': Fore.CYAN,
        'INFO': Fore.GREEN,
        'WARNING': Fore.YELLOW,
        'ERROR': Fore.RED,
        'CRITICAL': Fore.MAGENTA,
    }
    
    def format(self, record):
        # 获取颜色
        color = self.COLORS.get(record.levelname, '')
        
        # 格式化消息
        formatted = super().format(record)
        
        # 添加颜色
        if color:
            formatted = f"{color}{formatted}{Style.RESET_ALL}"
        
        return formatted

def setup_logging(log_level=logging.INFO, log_file: Optional[Path] = None):
    """设置日志系统"""
    
    # 创建根日志记录器
    logger = logging.getLogger()
    logger.setLevel(log_level)
    
    # 清除现有处理器
    for handler in logger.handlers:
        logger.removeHandler(handler)
    
    # 控制台处理器（彩色）
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(log_level)
    console_formatter = ColoredFormatter(
        '%(asctime)s | %(levelname)-8s | %(name)s | %(message)s',
        datefmt='%H:%M:%S'
    )
    console_handler.setFormatter(console_formatter)
    logger.addHandler(console_handler)
    
    # 文件处理器（如果指定了文件）
    if log_file:
        log_file.parent.mkdir(parents=True, exist_ok=True)
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)  # 文件中记录所有日志
        file_formatter = logging.Formatter(
            '%(asctime)s | %(levelname)-8s | %(name)s | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(file_formatter)
        logger.addHandler(file_handler)

def print_colored(text: str, color: str = None, style: str = None):
    """打印彩色文本"""
    color_map = {
        'red': Fore.RED,
        'green': Fore.GREEN,
        'yellow': Fore.YELLOW,
        'blue': Fore.BLUE,
        'magenta': Fore.MAGENTA,
        'cyan': Fore.CYAN,
        'white': Fore.WHITE,
    }
    
    style_map = {
        'bright': Style.BRIGHT,
        'dim': Style.DIM,
    }
    
    formatted_text = text
    
    if color and color.lower() in color_map:
        formatted_text = f"{color_map[color.lower()]}{formatted_text}"
    
    if style and style.lower() in style_map:
        formatted_text = f"{style_map[style.lower()]}{formatted_text}"
    
    if color or style:
        formatted_text = f"{formatted_text}{Style.RESET_ALL}"
    
    print(formatted_text)

def print_banner():
    """打印程序横幅"""
    banner = f"""
{Fore.CYAN}╔══════════════════════════════════════════════════════════════╗
║                    Wallpaper Updater                        ║
║                   自动壁纸更新程序                            ║
║                                                              ║
║              Powered by Wallhaven API                       ║
╚══════════════════════════════════════════════════════════════╝{Style.RESET_ALL}
"""
    print(banner)

def print_help():
    """打印帮助信息"""
    help_text = f"""
{Fore.GREEN}可用命令:{Style.RESET_ALL}
  {Fore.YELLOW}update{Style.RESET_ALL}   (u) - 立即更新壁纸
  {Fore.YELLOW}status{Style.RESET_ALL}   (s) - 查看当前状态
  {Fore.YELLOW}info{Style.RESET_ALL}     (i) - 显示程序信息
  {Fore.YELLOW}config{Style.RESET_ALL}   (c) - 配置管理
  {Fore.YELLOW}history{Style.RESET_ALL}  (h) - 查看壁纸历史
  {Fore.YELLOW}test{Style.RESET_ALL}     (t) - 测试API连接
  {Fore.YELLOW}help{Style.RESET_ALL}        - 显示此帮助
  {Fore.YELLOW}quit{Style.RESET_ALL}     (q) - 退出程序

{Fore.GREEN}配置子命令:{Style.RESET_ALL}
  config show           - 显示当前配置
  config set <key> <value> - 设置配置项
  config reset          - 重置为默认配置
"""
    print(help_text)

def format_file_size(size_bytes: int) -> str:
    """格式化文件大小"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    size = float(size_bytes)
    
    while size >= 1024.0 and i < len(size_names) - 1:
        size /= 1024.0
        i += 1
    
    return f"{size:.1f} {size_names[i]}"

def format_duration(seconds: int) -> str:
    """格式化时间间隔"""
    if seconds < 60:
        return f"{seconds}秒"
    elif seconds < 3600:
        minutes = seconds // 60
        return f"{minutes}分钟"
    elif seconds < 86400:
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        return f"{hours}小时{minutes}分钟"
    else:
        days = seconds // 86400
        hours = (seconds % 86400) // 3600
        return f"{days}天{hours}小时"

def get_system_info() -> dict:
    """获取系统信息"""
    try:
        import platform
        import psutil
        
        return {
            'platform': platform.system(),
            'platform_version': platform.version(),
            'architecture': platform.architecture()[0],
            'python_version': platform.python_version(),
            'cpu_count': psutil.cpu_count(),
            'memory_total': format_file_size(psutil.virtual_memory().total),
            'memory_available': format_file_size(psutil.virtual_memory().available),
        }
    except ImportError:
        return {
            'platform': os.name,
            'python_version': f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
        }

def ensure_directory_exists(path: Path):
    """确保目录存在"""
    try:
        path.mkdir(parents=True, exist_ok=True)
    except Exception as e:
        logging.error(f"Failed to create directory {path}: {e}")
        raise

def safe_filename(filename: str) -> str:
    """生成安全的文件名"""
    # Windows不允许的字符
    invalid_chars = '<>:"/\\|?*'
    
    for char in invalid_chars:
        filename = filename.replace(char, '_')
    
    # 移除前后空格
    filename = filename.strip()
    
    # 限制长度
    if len(filename) > 200:
        name, ext = os.path.splitext(filename)
        filename = name[:200-len(ext)] + ext
    
    return filename

def get_app_data_dir() -> Path:
    """获取应用数据目录"""
    if os.name == 'nt':  # Windows
        app_data = Path(os.environ.get('APPDATA', ''))
        return app_data / 'WallpaperUpdater'
    else:
        home = Path.home()
        return home / '.wallpaper-updater'

def validate_time_format(time_str: str) -> bool:
    """验证时间格式 HH:MM"""
    try:
        time_parts = time_str.split(':')
        if len(time_parts) != 2:
            return False
        
        hour = int(time_parts[0])
        minute = int(time_parts[1])
        
        return 0 <= hour < 24 and 0 <= minute < 60
    except (ValueError, IndexError):
        return False

def print_status_table(data: dict):
    """打印状态表格"""
    max_key_length = max(len(str(key)) for key in data.keys())
    
    print(f"\n{Fore.GREEN}{'='*50}{Style.RESET_ALL}")
    for key, value in data.items():
        key_str = str(key).ljust(max_key_length)
        print(f"{Fore.CYAN}{key_str}{Style.RESET_ALL} : {value}")
    print(f"{Fore.GREEN}{'='*50}{Style.RESET_ALL}\n")

def ask_yes_no(question: str, default: bool = True) -> bool:
    """询问是/否问题"""
    default_str = "Y/n" if default else "y/N"
    
    while True:
        answer = input(f"{question} ({default_str}): ").strip().lower()
        
        if answer == "":
            return default
        elif answer in ['y', 'yes', '是']:
            return True
        elif answer in ['n', 'no', '否']:
            return False
        else:
            print("请输入 y/yes 或 n/no")

def handle_keyboard_interrupt(func):
    """键盘中断处理装饰器"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except KeyboardInterrupt:
            print(f"\n{Fore.YELLOW}程序被用户中断{Style.RESET_ALL}")
            return False
    return wrapper

# 测试函数
if __name__ == "__main__":
    setup_logging()
    
    print_banner()
    print_help()
    
    test_data = {
        '系统': 'Windows 11',
        '内存': '16 GB',
        '存储': '512 GB',
        '状态': '正常运行'
    }
    
    print_status_table(test_data)
    
    print_colored("这是红色文本", "red")
    print_colored("这是绿色加粗文本", "green", "bright")
    
    print(f"文件大小: {format_file_size(1536000)}")
    print(f"时间间隔: {format_duration(3665)}")
    
    print("系统信息:", get_system_info())