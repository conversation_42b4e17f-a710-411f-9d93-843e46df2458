package com.wallpaper.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Properties;

public class AppConfig {
    private static final Logger logger = LoggerFactory.getLogger(AppConfig.class);
    private static final String DEFAULT_CONFIG_FILE = "config.properties";
    private static final String USER_CONFIG_FILE = "user-config.properties";
    
    private final Properties properties;
    
    public AppConfig() {
        this.properties = new Properties();
        loadConfiguration();
    }
    
    private void loadConfiguration() {
        // Load default configuration
        try (InputStream is = AppConfig.class.getClassLoader().getResourceAsStream(DEFAULT_CONFIG_FILE)) {
            if (is != null) {
                properties.load(is);
                logger.info("Loaded default configuration");
            }
        } catch (IOException e) {
            logger.error("Failed to load default configuration", e);
        }
        
        // Load user configuration if exists
        Path userConfigPath = Paths.get(USER_CONFIG_FILE);
        if (Files.exists(userConfigPath)) {
            try (InputStream is = Files.newInputStream(userConfigPath)) {
                Properties userProps = new Properties();
                userProps.load(is);
                properties.putAll(userProps);
                logger.info("Loaded user configuration from {}", USER_CONFIG_FILE);
            } catch (IOException e) {
                logger.error("Failed to load user configuration", e);
            }
        }
    }
    
    // Wallhaven API Configuration
    public String getWallhavenBaseUrl() {
        return properties.getProperty("wallhaven.baseUrl", "https://wallhaven.cc/api/v1");
    }
    
    public String getWallhavenApiKey() {
        return properties.getProperty("wallhaven.apiKey", "");
    }
    
    public String getWallhavenCategories() {
        return properties.getProperty("wallhaven.categories", "general");
    }
    
    public String getWallhavenPurity() {
        return properties.getProperty("wallhaven.purity", "sfw");
    }
    
    public String getWallhavenSorting() {
        return properties.getProperty("wallhaven.sorting", "toplist");
    }
    
    public String getWallhavenTopRange() {
        return properties.getProperty("wallhaven.topRange", "1M");
    }
    
    public String getWallhavenMinResolution() {
        return properties.getProperty("wallhaven.minResolution", "1920x1080");
    }
    
    public String getWallhavenRatios() {
        return properties.getProperty("wallhaven.ratios", "16x9");
    }
    
    // Update Settings
    public String getUpdateScheduleCron() {
        return properties.getProperty("update.schedule.cron", "0 0 9 * * ?");
    }
    
    public boolean isUpdateOnStartup() {
        return Boolean.parseBoolean(properties.getProperty("update.onStartup", "true"));
    }
    
    public boolean isRandomize() {
        return Boolean.parseBoolean(properties.getProperty("update.randomize", "true"));
    }
    
    // Storage Settings
    public String getStoragePath() {
        return properties.getProperty("storage.path", "./wallpapers");
    }
    
    public int getMaxFiles() {
        return Integer.parseInt(properties.getProperty("storage.maxFiles", "50"));
    }
    
    public boolean isCleanupOldFiles() {
        return Boolean.parseBoolean(properties.getProperty("storage.cleanupOldFiles", "true"));
    }
    
    // Windows Settings
    public String getWindowsWallpaperStyle() {
        return properties.getProperty("windows.wallpaperStyle", "fill").toUpperCase();
    }
    
    public String getWindowsMultiMonitor() {
        return properties.getProperty("windows.multiMonitor", "same");
    }
    
    // Utility methods
    public String getProperty(String key) {
        return properties.getProperty(key);
    }
    
    public String getProperty(String key, String defaultValue) {
        return properties.getProperty(key, defaultValue);
    }
    
    public void setProperty(String key, String value) {
        properties.setProperty(key, value);
    }
    
    public void saveUserConfig() {
        Path userConfigPath = Paths.get(USER_CONFIG_FILE);
        try {
            properties.store(Files.newOutputStream(userConfigPath), "User configuration for Wallpaper Updater");
            logger.info("Saved user configuration to {}", USER_CONFIG_FILE);
        } catch (IOException e) {
            logger.error("Failed to save user configuration", e);
        }
    }
}