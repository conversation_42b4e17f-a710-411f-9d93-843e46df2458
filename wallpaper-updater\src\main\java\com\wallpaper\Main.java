package com.wallpaper;

import com.wallpaper.api.WallhavenClient;
import com.wallpaper.config.AppConfig;
import com.wallpaper.scheduler.UpdateScheduler;
import com.wallpaper.service.WallpaperDownloader;
import com.wallpaper.service.WallpaperSetter;
import org.quartz.SchedulerException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Scanner;

public class Main {
    private static final Logger logger = LoggerFactory.getLogger(Main.class);
    
    public static void main(String[] args) {
        logger.info("=== Wallpaper Updater Started ===");
        
        // Check if running on Windows
        if (!WallpaperSetter.isWindowsOS()) {
            logger.error("This application only supports Windows operating system");
            System.err.println("Error: This application only supports Windows operating system");
            System.exit(1);
        }
        
        try {
            // Load configuration
            AppConfig config = new AppConfig();
            logger.info("Configuration loaded successfully");
            
            // Check API key
            String apiKey = config.getWallhavenApiKey();
            if (apiKey == null || apiKey.trim().isEmpty()) {
                logger.warn("No Wallhaven API key configured. Some features may be limited.");
                System.out.println("Warning: No Wallhaven API key configured in config.properties");
                System.out.println("You can get a free API key from https://wallhaven.cc/settings/account");
            }
            
            // Initialize components
            WallhavenClient apiClient = new WallhavenClient(
                    config.getWallhavenBaseUrl(),
                    apiKey
            );
            
            WallpaperDownloader downloader = new WallpaperDownloader(
                    config.getStoragePath(),
                    config.getMaxFiles(),
                    config.isCleanupOldFiles()
            );
            
            WallpaperSetter.WallpaperStyle style = WallpaperSetter.WallpaperStyle.valueOf(
                    config.getWindowsWallpaperStyle()
            );
            WallpaperSetter setter = new WallpaperSetter(style);
            
            // Initialize scheduler
            UpdateScheduler scheduler = new UpdateScheduler(apiClient, downloader, setter, config);
            
            // Start scheduler
            scheduler.start();
            
            // Interactive mode
            printHelp();
            Scanner scanner = new Scanner(System.in);
            boolean running = true;
            
            while (running) {
                String command = scanner.nextLine().trim().toLowerCase();
                
                switch (command) {
                    case "update":
                    case "u":
                        System.out.println("Updating wallpaper...");
                        scheduler.updateWallpaper();
                        break;
                        
                    case "status":
                    case "s":
                        printStatus(setter, downloader, config);
                        break;
                        
                    case "help":
                    case "h":
                        printHelp();
                        break;
                        
                    case "quit":
                    case "q":
                    case "exit":
                        running = false;
                        break;
                        
                    default:
                        System.out.println("Unknown command. Type 'help' for available commands.");
                }
            }
            
            // Shutdown
            logger.info("Shutting down...");
            scheduler.stop();
            System.out.println("Goodbye!");
            
        } catch (SchedulerException e) {
            logger.error("Failed to initialize scheduler", e);
            System.err.println("Error: Failed to initialize scheduler - " + e.getMessage());
            System.exit(1);
        } catch (Exception e) {
            logger.error("Unexpected error", e);
            System.err.println("Error: " + e.getMessage());
            e.printStackTrace();
            System.exit(1);
        }
    }
    
    private static void printHelp() {
        System.out.println("\n=== Wallpaper Updater Commands ===");
        System.out.println("update (u)  - Update wallpaper immediately");
        System.out.println("status (s)  - Show current status");
        System.out.println("help (h)    - Show this help message");
        System.out.println("quit (q)    - Exit the application");
        System.out.println("================================\n");
    }
    
    private static void printStatus(WallpaperSetter setter, WallpaperDownloader downloader, AppConfig config) {
        System.out.println("\n=== Status ===");
        System.out.println("Current wallpaper: " + setter.getCurrentWallpaperPath());
        System.out.println("Downloaded wallpapers: " + downloader.getAllWallpapers().size());
        System.out.println("Update schedule: " + config.getUpdateScheduleCron());
        System.out.println("Storage path: " + config.getStoragePath());
        System.out.println("==============\n");
    }
}