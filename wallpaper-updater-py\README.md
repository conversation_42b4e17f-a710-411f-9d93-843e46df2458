# Wallpaper Updater (Python版)

🎨 基于Python开发的自动壁纸更新程序，使用Wallhaven API获取高质量壁纸并自动设置为Windows桌面背景。

## ✨ 功能特性

- 🔗 **Wallhaven API集成** - 访问海量高质量壁纸资源
- ⏰ **定时自动更新** - 支持每日定时更新壁纸
- 🎯 **智能筛选** - 按分类、分辨率、宽高比等条件筛选
- 💾 **本地缓存管理** - 自动下载并管理本地壁纸缓存
- 🖼️ **多种适配模式** - 支持填充、适应、拉伸等壁纸样式
- 🎮 **交互式界面** - 友好的命令行交互界面
- 📊 **状态监控** - 实时查看程序运行状态和壁纸历史
- 🔄 **启动更新** - 程序启动时自动更新壁纸
- 🎲 **随机选择** - 可选择热门壁纸或随机壁纸

## 🚀 快速开始

### 系统要求

- Windows 7/8/10/11
- Python 3.7+ （打包版无需Python环境）

### 方法一：直接运行exe（推荐）

1. 下载最新的 `wallpaper-updater.exe`
2. 双击运行程序
3. 首次运行会创建默认配置文件

### 方法二：从源码运行

1. **克隆/下载项目**
   ```bash
   git clone <repository-url>
   cd wallpaper-updater-py
   ```

2. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

3. **运行程序**
   ```bash
   python main.py
   ```

## ⚙️ 配置说明

程序首次运行会创建 `config.ini` 配置文件，主要配置项：

### Wallhaven API 配置
```ini
[wallhaven]
base_url = https://wallhaven.cc/api/v1
api_key =                    # 可选：API密钥，获取更多功能
categories = general,anime   # 分类：general, anime, people
purity = sfw                 # 纯度：sfw, sketchy, nsfw
sorting = toplist           # 排序：toplist, random, date_added, views, favorites
min_resolution = 1920x1080  # 最小分辨率
ratios = 16x9,16x10         # 宽高比
```

### 更新设置
```ini
[update]
schedule_hour = 9           # 更新时间（小时）
schedule_minute = 0         # 更新时间（分钟）
update_on_startup = true    # 启动时更新
randomize = true           # 随机选择壁纸
```

### 存储设置
```ini
[storage]
path = wallpapers          # 壁纸存储目录
max_files = 50            # 最大保留文件数
cleanup_old_files = true  # 自动清理旧文件
```

### Windows设置
```ini
[windows]
wallpaper_style = fill     # 壁纸样式：fill, fit, stretch, tile, center, span
```

## 🎯 获取API密钥（可选但推荐）

1. 访问 [Wallhaven](https://wallhaven.cc) 并注册账号
2. 进入 [设置页面](https://wallhaven.cc/settings/account)
3. 在API部分生成API密钥
4. 将密钥填入 `config.ini` 的 `api_key` 字段

> API密钥可以获得更高的访问限制和更多功能

## 💻 使用说明

程序启动后进入交互式命令行界面：

### 主要命令
- `update` (u) - 立即更新壁纸
- `status` (s) - 查看当前状态
- `info` (i) - 显示配置信息
- `history` (h) - 查看壁纸历史
- `test` (t) - 测试API连接
- `help` - 显示帮助信息
- `quit` (q) - 退出程序

### 配置管理
- `config show` - 显示当前配置
- `config set <key> <value>` - 设置配置项
- `config reset` - 重置配置

## 📦 编译打包

### 打包为exe文件

1. **安装打包工具**
   ```bash
   pip install pyinstaller
   ```

2. **执行打包脚本**
   ```bash
   python build.py
   ```

3. **可选参数**
   ```bash
   python build.py --debug      # 生成调试版本
   python build.py --onedir     # 生成目录分发版
   python build.py --clean      # 清理构建文件
   ```

4. **输出文件**
   - 单文件版：`dist/wallpaper-updater.exe`
   - 安装脚本：`dist/install.bat`

### 自动安装

运行 `dist/install.bat` 可以：
- 安装程序到Program Files
- 创建桌面快捷方式
- 创建开始菜单快捷方式

## 📂 项目结构

```
wallpaper-updater-py/
├── main.py                 # 主程序入口
├── config.ini             # 配置文件
├── requirements.txt       # 依赖列表
├── build.py              # 打包脚本
├── setup.py              # 安装脚本
├── src/                  # 源代码目录
│   ├── config.py         # 配置管理
│   ├── wallhaven_api.py  # API客户端
│   ├── downloader.py     # 下载器
│   ├── wallpaper_setter.py # 壁纸设置
│   ├── scheduler.py      # 任务调度
│   └── utils.py          # 工具函数
├── logs/                 # 日志目录
├── wallpapers/           # 壁纸缓存目录
└── README.md            # 说明文档
```

## 🛠️ 开发相关

### 依赖说明

| 依赖库 | 用途 | 版本要求 |
|--------|------|----------|
| requests | HTTP请求 | ≥2.31.0 |
| Pillow | 图片处理 | ≥10.0.0 |
| schedule | 定时任务 | ≥1.2.0 |
| pywin32 | Windows API | ≥306 |
| colorama | 终端颜色 | ≥0.4.6 |

### 代码结构

- **配置管理** (`config.py`) - 处理INI配置文件的读写
- **API客户端** (`wallhaven_api.py`) - 封装Wallhaven API调用
- **下载器** (`downloader.py`) - 处理壁纸下载和缓存管理
- **壁纸设置** (`wallpaper_setter.py`) - 调用Windows API设置壁纸
- **任务调度** (`scheduler.py`) - 处理定时任务的调度
- **工具函数** (`utils.py`) - 日志、UI等通用工具

## 🐛 故障排除

### 常见问题

1. **程序无法启动**
   - 确认Python版本 ≥ 3.7
   - 检查是否安装了所有依赖：`pip install -r requirements.txt`

2. **无法下载壁纸**
   - 检查网络连接
   - 运行 `test` 命令测试API连接
   - 确认防火墙设置

3. **无法设置壁纸**
   - 确认运行在Windows系统上
   - 尝试以管理员权限运行
   - 检查壁纸文件是否损坏

4. **定时任务不执行**
   - 确认程序持续运行
   - 检查系统时间设置
   - 查看日志文件了解详细错误

### 日志文件

程序运行日志保存在 `logs/wallpaper_updater.log`，包含详细的运行信息和错误日志。

### 清理数据

如需完全清理程序数据：
1. 删除程序目录
2. 删除配置文件 `config.ini`
3. 删除壁纸缓存目录 `wallpapers/`
4. 删除日志目录 `logs/`

## 📄 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📞 支持

如有问题或建议，请：
1. 查看本文档的故障排除部分
2. 检查日志文件中的错误信息
3. 在GitHub上提交Issue

---

**Enjoy your beautiful wallpapers! 🎨**