package com.wallpaper.scheduler;

import com.wallpaper.api.WallhavenClient;
import com.wallpaper.api.models.Wallpaper;
import com.wallpaper.config.AppConfig;
import com.wallpaper.service.WallpaperDownloader;
import com.wallpaper.service.WallpaperSetter;
import org.quartz.*;
import org.quartz.impl.StdSchedulerFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.concurrent.CompletableFuture;

public class UpdateScheduler {
    private static final Logger logger = LoggerFactory.getLogger(UpdateScheduler.class);
    
    private final Scheduler scheduler;
    private final WallhavenClient apiClient;
    private final WallpaperDownloader downloader;
    private final WallpaperSetter setter;
    private final AppConfig config;
    
    public UpdateScheduler(WallhavenClient apiClient, WallpaperDownloader downloader, 
                         WallpaperSetter setter, AppConfig config) throws SchedulerException {
        this.apiClient = apiClient;
        this.downloader = downloader;
        this.setter = setter;
        this.config = config;
        this.scheduler = StdSchedulerFactory.getDefaultScheduler();
    }
    
    public void start() throws SchedulerException {
        JobDetail job = JobBuilder.newJob(WallpaperUpdateJob.class)
                .withIdentity("wallpaperUpdateJob", "wallpaper")
                .build();
        
        // Pass dependencies to job
        job.getJobDataMap().put("apiClient", apiClient);
        job.getJobDataMap().put("downloader", downloader);
        job.getJobDataMap().put("setter", setter);
        job.getJobDataMap().put("config", config);
        
        String cronExpression = config.getUpdateScheduleCron();
        CronTrigger trigger = TriggerBuilder.newTrigger()
                .withIdentity("wallpaperUpdateTrigger", "wallpaper")
                .withSchedule(CronScheduleBuilder.cronSchedule(cronExpression))
                .build();
        
        scheduler.scheduleJob(job, trigger);
        scheduler.start();
        
        logger.info("Scheduler started with cron expression: {}", cronExpression);
        
        // Update on startup if configured
        if (config.isUpdateOnStartup()) {
            logger.info("Updating wallpaper on startup");
            updateWallpaper();
        }
    }
    
    public void stop() throws SchedulerException {
        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown();
            logger.info("Scheduler stopped");
        }
    }
    
    public void updateWallpaper() {
        try {
            WallhavenClient.SearchParams searchParams = WallhavenClient.SearchParams.builder()
                    .categories(config.getWallhavenCategories())
                    .purity(config.getWallhavenPurity())
                    .sorting(config.getWallhavenSorting())
                    .topRange(config.getWallhavenTopRange())
                    .minResolution(config.getWallhavenMinResolution())
                    .ratios(config.getWallhavenRatios());
            
            Wallpaper wallpaper = config.isRandomize() 
                    ? apiClient.getRandomWallpaper(searchParams)
                    : apiClient.searchWallpapers(searchParams).get(0);
            
            logger.info("Selected wallpaper: {} ({})", wallpaper.getId(), wallpaper.getResolution());
            
            CompletableFuture<File> downloadFuture = downloader.downloadWallpaper(wallpaper);
            File wallpaperFile = downloadFuture.get();
            
            WallpaperSetter.WallpaperStyle style = WallpaperSetter.WallpaperStyle.valueOf(
                    config.getWindowsWallpaperStyle()
            );
            
            boolean success = setter.setWallpaper(wallpaperFile, style);
            
            if (success) {
                logger.info("Successfully updated wallpaper");
            } else {
                logger.error("Failed to set wallpaper");
            }
            
        } catch (Exception e) {
            logger.error("Error updating wallpaper", e);
        }
    }
    
    public static class WallpaperUpdateJob implements Job {
        private static final Logger logger = LoggerFactory.getLogger(WallpaperUpdateJob.class);
        
        @Override
        public void execute(JobExecutionContext context) throws JobExecutionException {
            logger.info("Executing wallpaper update job");
            
            JobDataMap dataMap = context.getJobDetail().getJobDataMap();
            WallhavenClient apiClient = (WallhavenClient) dataMap.get("apiClient");
            WallpaperDownloader downloader = (WallpaperDownloader) dataMap.get("downloader");
            WallpaperSetter setter = (WallpaperSetter) dataMap.get("setter");
            AppConfig config = (AppConfig) dataMap.get("config");
            
            try {
                WallhavenClient.SearchParams searchParams = WallhavenClient.SearchParams.builder()
                        .categories(config.getWallhavenCategories())
                        .purity(config.getWallhavenPurity())
                        .sorting(config.getWallhavenSorting())
                        .topRange(config.getWallhavenTopRange())
                        .minResolution(config.getWallhavenMinResolution())
                        .ratios(config.getWallhavenRatios());
                
                Wallpaper wallpaper = config.isRandomize() 
                        ? apiClient.getRandomWallpaper(searchParams)
                        : apiClient.searchWallpapers(searchParams).get(0);
                
                logger.info("Selected wallpaper: {} ({})", wallpaper.getId(), wallpaper.getResolution());
                
                CompletableFuture<File> downloadFuture = downloader.downloadWallpaper(wallpaper);
                File wallpaperFile = downloadFuture.get();
                
                WallpaperSetter.WallpaperStyle style = WallpaperSetter.WallpaperStyle.valueOf(
                        config.getWindowsWallpaperStyle()
                );
                
                boolean success = setter.setWallpaper(wallpaperFile, style);
                
                if (success) {
                    logger.info("Successfully updated wallpaper");
                } else {
                    logger.error("Failed to set wallpaper");
                }
                
            } catch (Exception e) {
                logger.error("Error in wallpaper update job", e);
                throw new JobExecutionException(e);
            }
        }
    }
}