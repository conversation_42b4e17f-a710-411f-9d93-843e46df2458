package com.wallpaper.api;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wallpaper.api.models.Wallpaper;
import com.wallpaper.api.models.WallpaperResponse;
import okhttp3.HttpUrl;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;

public class WallhavenClient {
    private static final Logger logger = LoggerFactory.getLogger(WallhavenClient.class);
    
    private final String baseUrl;
    private final String apiKey;
    private final OkHttpClient httpClient;
    private final ObjectMapper objectMapper;
    
    public WallhavenClient(String baseUrl, String apiKey) {
        this.baseUrl = baseUrl;
        this.apiKey = apiKey;
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .build();
        this.objectMapper = new ObjectMapper();
    }
    
    public List<Wallpaper> searchWallpapers(SearchParams params) throws IOException {
        HttpUrl.Builder urlBuilder = HttpUrl.parse(baseUrl + "/search").newBuilder();
        
        if (params.getCategories() != null) {
            urlBuilder.addQueryParameter("categories", params.getCategories());
        }
        if (params.getPurity() != null) {
            urlBuilder.addQueryParameter("purity", params.getPurity());
        }
        if (params.getSorting() != null) {
            urlBuilder.addQueryParameter("sorting", params.getSorting());
        }
        if (params.getTopRange() != null) {
            urlBuilder.addQueryParameter("topRange", params.getTopRange());
        }
        if (params.getMinResolution() != null) {
            urlBuilder.addQueryParameter("atleast", params.getMinResolution());
        }
        if (params.getRatios() != null) {
            urlBuilder.addQueryParameter("ratios", params.getRatios());
        }
        if (params.getQuery() != null) {
            urlBuilder.addQueryParameter("q", params.getQuery());
        }
        if (params.getPage() != null) {
            urlBuilder.addQueryParameter("page", params.getPage().toString());
        }
        
        if (apiKey != null && !apiKey.isEmpty()) {
            urlBuilder.addQueryParameter("apikey", apiKey);
        }
        
        Request request = new Request.Builder()
                .url(urlBuilder.build())
                .get()
                .build();
        
        logger.info("Searching wallpapers with params: {}", urlBuilder.build());
        
        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected response code: " + response);
            }
            
            String responseBody = response.body().string();
            WallpaperResponse wallpaperResponse = objectMapper.readValue(responseBody, WallpaperResponse.class);
            
            logger.info("Found {} wallpapers", wallpaperResponse.getData().size());
            return wallpaperResponse.getData();
        }
    }
    
    public Wallpaper getWallpaper(String id) throws IOException {
        HttpUrl url = HttpUrl.parse(baseUrl + "/w/" + id).newBuilder()
                .addQueryParameter("apikey", apiKey)
                .build();
        
        Request request = new Request.Builder()
                .url(url)
                .get()
                .build();
        
        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected response code: " + response);
            }
            
            String responseBody = response.body().string();
            Map<String, Object> responseMap = objectMapper.readValue(responseBody, Map.class);
            return objectMapper.convertValue(responseMap.get("data"), Wallpaper.class);
        }
    }
    
    public Wallpaper getRandomWallpaper(SearchParams params) throws IOException {
        List<Wallpaper> wallpapers = searchWallpapers(params);
        if (wallpapers.isEmpty()) {
            throw new IOException("No wallpapers found with given parameters");
        }
        
        Random random = new Random();
        return wallpapers.get(random.nextInt(wallpapers.size()));
    }
    
    public static class SearchParams {
        private String categories;
        private String purity;
        private String sorting;
        private String topRange;
        private String minResolution;
        private String ratios;
        private String query;
        private Integer page;
        
        public static SearchParams builder() {
            return new SearchParams();
        }
        
        public SearchParams categories(String... categories) {
            this.categories = String.join("", categories);
            return this;
        }
        
        public SearchParams purity(String purity) {
            this.purity = purity;
            return this;
        }
        
        public SearchParams sorting(String sorting) {
            this.sorting = sorting;
            return this;
        }
        
        public SearchParams topRange(String topRange) {
            this.topRange = topRange;
            return this;
        }
        
        public SearchParams minResolution(String minResolution) {
            this.minResolution = minResolution;
            return this;
        }
        
        public SearchParams ratios(String ratios) {
            this.ratios = ratios;
            return this;
        }
        
        public SearchParams query(String query) {
            this.query = query;
            return this;
        }
        
        public SearchParams page(Integer page) {
            this.page = page;
            return this;
        }

        public String getCategories() {
            return categories;
        }

        public String getPurity() {
            return purity;
        }

        public String getSorting() {
            return sorting;
        }

        public String getTopRange() {
            return topRange;
        }

        public String getMinResolution() {
            return minResolution;
        }

        public String getRatios() {
            return ratios;
        }

        public String getQuery() {
            return query;
        }

        public Integer getPage() {
            return page;
        }
    }
}