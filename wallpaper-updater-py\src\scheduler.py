import schedule
import time
import threading
import logging
from datetime import datetime
from typing import Callable, Optional

class WallpaperScheduler:
    """壁纸更新调度器"""
    
    def __init__(self, update_callback: Callable[[], bool]):
        """
        初始化调度器
        
        Args:
            update_callback: 更新壁纸的回调函数，返回True表示成功
        """
        self.update_callback = update_callback
        self.is_running = False
        self.scheduler_thread = None
        self.stop_event = threading.Event()
        
        logging.info("Wallpaper scheduler initialized")
    
    def schedule_daily_update(self, hour: int, minute: int = 0):
        """
        设置每日定时更新
        
        Args:
            hour: 小时 (0-23)
            minute: 分钟 (0-59)
        """
        try:
            # 清除现有的调度任务
            schedule.clear('wallpaper')
            
            # 设置新的调度任务
            schedule.every().day.at(f"{hour:02d}:{minute:02d}").do(
                self._safe_update_wrapper
            ).tag('wallpaper')
            
            logging.info(f"Scheduled daily wallpaper update at {hour:02d}:{minute:02d}")
            
        except Exception as e:
            logging.error(f"Failed to schedule daily update: {e}")
            raise
    
    def schedule_hourly_update(self, minute: int = 0):
        """
        设置每小时更新
        
        Args:
            minute: 分钟 (0-59)
        """
        try:
            schedule.clear('wallpaper')
            
            schedule.every().hour.at(f":{minute:02d}").do(
                self._safe_update_wrapper
            ).tag('wallpaper')
            
            logging.info(f"Scheduled hourly wallpaper update at minute {minute}")
            
        except Exception as e:
            logging.error(f"Failed to schedule hourly update: {e}")
            raise
    
    def schedule_interval_update(self, interval_minutes: int):
        """
        设置间隔更新
        
        Args:
            interval_minutes: 更新间隔（分钟）
        """
        try:
            schedule.clear('wallpaper')
            
            schedule.every(interval_minutes).minutes.do(
                self._safe_update_wrapper
            ).tag('wallpaper')
            
            logging.info(f"Scheduled wallpaper update every {interval_minutes} minutes")
            
        except Exception as e:
            logging.error(f"Failed to schedule interval update: {e}")
            raise
    
    def schedule_custom(self, schedule_expression: str):
        """
        自定义调度表达式
        支持的格式示例：
        - "09:30" - 每天9:30
        - "monday.09:30" - 每周一9:30
        - "60.minutes" - 每60分钟
        """
        try:
            schedule.clear('wallpaper')
            
            if ':' in schedule_expression and '.' not in schedule_expression:
                # 每日定时：如 "09:30"
                hour, minute = map(int, schedule_expression.split(':'))
                self.schedule_daily_update(hour, minute)
                
            elif schedule_expression.lower().startswith(('monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday')):
                # 每周定时：如 "monday.09:30"
                day, time_part = schedule_expression.lower().split('.', 1)
                hour, minute = map(int, time_part.split(':'))
                
                day_method = getattr(schedule.every(), day)
                day_method.at(f"{hour:02d}:{minute:02d}").do(
                    self._safe_update_wrapper
                ).tag('wallpaper')
                
                logging.info(f"Scheduled weekly wallpaper update on {day} at {hour:02d}:{minute:02d}")
                
            elif schedule_expression.endswith('.minutes'):
                # 间隔更新：如 "60.minutes"
                interval = int(schedule_expression.split('.')[0])
                self.schedule_interval_update(interval)
                
            else:
                raise ValueError(f"Unsupported schedule expression: {schedule_expression}")
                
        except Exception as e:
            logging.error(f"Failed to parse custom schedule '{schedule_expression}': {e}")
            raise
    
    def _safe_update_wrapper(self):
        """安全的更新包装器，捕获异常"""
        try:
            logging.info("Scheduled wallpaper update starting...")
            success = self.update_callback()
            
            if success:
                logging.info("Scheduled wallpaper update completed successfully")
            else:
                logging.error("Scheduled wallpaper update failed")
                
        except Exception as e:
            logging.error(f"Error in scheduled wallpaper update: {e}")
    
    def start(self):
        """启动调度器"""
        if self.is_running:
            logging.warning("Scheduler is already running")
            return
        
        self.is_running = True
        self.stop_event.clear()
        
        # 启动调度器线程
        self.scheduler_thread = threading.Thread(target=self._scheduler_loop, daemon=True)
        self.scheduler_thread.start()
        
        logging.info("Wallpaper scheduler started")
    
    def stop(self):
        """停止调度器"""
        if not self.is_running:
            logging.warning("Scheduler is not running")
            return
        
        self.is_running = False
        self.stop_event.set()
        
        # 等待线程结束
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            self.scheduler_thread.join(timeout=5)
        
        # 清除所有调度任务
        schedule.clear('wallpaper')
        
        logging.info("Wallpaper scheduler stopped")
    
    def _scheduler_loop(self):
        """调度器主循环"""
        while self.is_running and not self.stop_event.is_set():
            try:
                schedule.run_pending()
                time.sleep(1)  # 每秒检查一次
            except Exception as e:
                logging.error(f"Error in scheduler loop: {e}")
                time.sleep(60)  # 发生错误时等待1分钟后重试
    
    def get_next_run_time(self) -> Optional[datetime]:
        """获取下次执行时间"""
        try:
            jobs = schedule.get_jobs('wallpaper')
            if not jobs:
                return None
            
            next_run = min(job.next_run for job in jobs)
            return next_run
            
        except Exception as e:
            logging.error(f"Failed to get next run time: {e}")
            return None
    
    def get_schedule_info(self) -> dict:
        """获取调度信息"""
        try:
            jobs = schedule.get_jobs('wallpaper')
            
            if not jobs:
                return {
                    'scheduled': False,
                    'jobs_count': 0,
                    'next_run': None,
                    'is_running': self.is_running
                }
            
            next_run = self.get_next_run_time()
            
            return {
                'scheduled': True,
                'jobs_count': len(jobs),
                'next_run': next_run.strftime('%Y-%m-%d %H:%M:%S') if next_run else None,
                'is_running': self.is_running
            }
            
        except Exception as e:
            logging.error(f"Failed to get schedule info: {e}")
            return {
                'scheduled': False,
                'jobs_count': 0,
                'next_run': None,
                'is_running': self.is_running,
                'error': str(e)
            }
    
    def force_update(self) -> bool:
        """强制执行一次更新"""
        try:
            logging.info("Force updating wallpaper...")
            return self.update_callback()
        except Exception as e:
            logging.error(f"Force update failed: {e}")
            return False

# 测试函数
def test_scheduler():
    """测试调度器"""
    def dummy_update():
        print(f"[{datetime.now()}] Wallpaper updated!")
        return True
    
    scheduler = WallpaperScheduler(dummy_update)
    
    print("Testing scheduler...")
    
    # 测试每分钟更新
    scheduler.schedule_interval_update(1)
    scheduler.start()
    
    print("Scheduler started, will update every minute")
    print(f"Next run: {scheduler.get_next_run_time()}")
    print(f"Schedule info: {scheduler.get_schedule_info()}")
    
    try:
        time.sleep(65)  # 等待一分钟多一点
    except KeyboardInterrupt:
        pass
    
    scheduler.stop()
    print("Scheduler stopped")

if __name__ == "__main__":
    # 设置基本日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    test_scheduler()