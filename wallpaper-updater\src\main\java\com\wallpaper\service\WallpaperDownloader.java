package com.wallpaper.service;

import com.wallpaper.api.models.Wallpaper;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.file.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

public class WallpaperDownloader {
    private static final Logger logger = LoggerFactory.getLogger(WallpaperDownloader.class);
    
    private final OkHttpClient httpClient;
    private final String storagePath;
    private final int maxFiles;
    private final boolean cleanupOldFiles;
    
    public WallpaperDownloader(String storagePath, int maxFiles, boolean cleanupOldFiles) {
        this.storagePath = storagePath;
        this.maxFiles = maxFiles;
        this.cleanupOldFiles = cleanupOldFiles;
        
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(60, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .build();
        
        initStorageDirectory();
    }
    
    private void initStorageDirectory() {
        try {
            Path path = Paths.get(storagePath);
            if (!Files.exists(path)) {
                Files.createDirectories(path);
                logger.info("Created storage directory: {}", storagePath);
            }
        } catch (IOException e) {
            logger.error("Failed to create storage directory", e);
            throw new RuntimeException("Failed to create storage directory", e);
        }
    }
    
    public CompletableFuture<File> downloadWallpaper(Wallpaper wallpaper) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                String fileUrl = wallpaper.getPath();
                String fileName = generateFileName(wallpaper);
                Path filePath = Paths.get(storagePath, fileName);
                
                if (Files.exists(filePath)) {
                    logger.info("Wallpaper already exists: {}", fileName);
                    return filePath.toFile();
                }
                
                logger.info("Downloading wallpaper: {} from {}", fileName, fileUrl);
                
                Request request = new Request.Builder()
                        .url(fileUrl)
                        .build();
                
                try (Response response = httpClient.newCall(request).execute()) {
                    if (!response.isSuccessful()) {
                        throw new IOException("Failed to download wallpaper: " + response);
                    }
                    
                    try (InputStream in = response.body().byteStream();
                         OutputStream out = Files.newOutputStream(filePath)) {
                        
                        byte[] buffer = new byte[8192];
                        long totalBytes = 0;
                        int bytesRead;
                        
                        while ((bytesRead = in.read(buffer)) != -1) {
                            out.write(buffer, 0, bytesRead);
                            totalBytes += bytesRead;
                        }
                        
                        logger.info("Downloaded {} bytes to {}", totalBytes, fileName);
                    }
                }
                
                if (cleanupOldFiles) {
                    cleanupOldWallpapers();
                }
                
                return filePath.toFile();
                
            } catch (Exception e) {
                logger.error("Failed to download wallpaper: {}", wallpaper.getId(), e);
                throw new RuntimeException("Download failed", e);
            }
        });
    }
    
    private String generateFileName(Wallpaper wallpaper) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String fileType = wallpaper.getFileType().toLowerCase();
        if (!fileType.startsWith(".")) {
            fileType = "." + fileType;
        }
        return String.format("wallpaper_%s_%s%s", wallpaper.getId(), timestamp, fileType);
    }
    
    private void cleanupOldWallpapers() {
        try {
            List<Path> files = Files.list(Paths.get(storagePath))
                    .filter(Files::isRegularFile)
                    .filter(p -> p.getFileName().toString().startsWith("wallpaper_"))
                    .collect(Collectors.toList());
            
            if (files.size() > maxFiles) {
                files.sort((a, b) -> {
                    try {
                        return Files.getLastModifiedTime(a).compareTo(Files.getLastModifiedTime(b));
                    } catch (IOException e) {
                        return 0;
                    }
                });
                
                int filesToDelete = files.size() - maxFiles;
                for (int i = 0; i < filesToDelete; i++) {
                    Path fileToDelete = files.get(i);
                    Files.delete(fileToDelete);
                    logger.info("Deleted old wallpaper: {}", fileToDelete.getFileName());
                }
            }
        } catch (IOException e) {
            logger.error("Failed to cleanup old wallpapers", e);
        }
    }
    
    public File getLatestWallpaper() {
        try {
            Optional<Path> latestFile = Files.list(Paths.get(storagePath))
                    .filter(Files::isRegularFile)
                    .filter(p -> p.getFileName().toString().startsWith("wallpaper_"))
                    .max((a, b) -> {
                        try {
                            return Files.getLastModifiedTime(a).compareTo(Files.getLastModifiedTime(b));
                        } catch (IOException e) {
                            return 0;
                        }
                    });
            
            return latestFile.map(Path::toFile).orElse(null);
        } catch (IOException e) {
            logger.error("Failed to get latest wallpaper", e);
            return null;
        }
    }
    
    public List<File> getAllWallpapers() {
        try {
            return Files.list(Paths.get(storagePath))
                    .filter(Files::isRegularFile)
                    .filter(p -> p.getFileName().toString().startsWith("wallpaper_"))
                    .sorted((a, b) -> {
                        try {
                            return Files.getLastModifiedTime(b).compareTo(Files.getLastModifiedTime(a));
                        } catch (IOException e) {
                            return 0;
                        }
                    })
                    .map(Path::toFile)
                    .collect(Collectors.toList());
        } catch (IOException e) {
            logger.error("Failed to list wallpapers", e);
            return Collections.emptyList();
        }
    }
}