import requests
import random
import logging
from typing import Dict, List, Optional

class WallhavenAPI:
    def __init__(self, base_url: str, api_key: str = None):
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Wallpaper-Updater-Python/1.0'
        })
    
    def search_wallpapers(self, params: Dict, page: int = 1) -> List[Dict]:
        """搜索壁纸"""
        search_params = params.copy()
        search_params['page'] = page
        
        url = f"{self.base_url}/search"
        
        try:
            logging.info(f"Searching wallpapers with params: {search_params}")
            response = self.session.get(url, params=search_params)
            response.raise_for_status()
            
            data = response.json()
            wallpapers = data.get('data', [])
            
            logging.info(f"Found {len(wallpapers)} wallpapers")
            return wallpapers
            
        except requests.RequestException as e:
            logging.error(f"Failed to search wallpapers: {e}")
            raise
        except Exception as e:
            logging.error(f"Unexpected error in search: {e}")
            raise
    
    def get_wallpaper(self, wallpaper_id: str) -> Optional[Dict]:
        """获取指定ID的壁纸信息"""
        url = f"{self.base_url}/w/{wallpaper_id}"
        params = {}
        
        if self.api_key:
            params['apikey'] = self.api_key
        
        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            return data.get('data')
            
        except requests.RequestException as e:
            logging.error(f"Failed to get wallpaper {wallpaper_id}: {e}")
            return None
    
    def get_random_wallpaper(self, search_params: Dict) -> Optional[Dict]:
        """获取随机壁纸"""
        try:
            # 先搜索获取壁纸列表
            wallpapers = self.search_wallpapers(search_params)
            
            if not wallpapers:
                logging.warning("No wallpapers found for random selection")
                return None
            
            # 随机选择一张
            selected = random.choice(wallpapers)
            logging.info(f"Selected random wallpaper: {selected['id']} ({selected.get('resolution', 'unknown')})")
            
            return selected
            
        except Exception as e:
            logging.error(f"Failed to get random wallpaper: {e}")
            return None
    
    def get_popular_wallpaper(self, search_params: Dict) -> Optional[Dict]:
        """获取热门壁纸（第一张）"""
        try:
            # 设置为热门排序
            params = search_params.copy()
            params['sorting'] = 'toplist'
            
            wallpapers = self.search_wallpapers(params)
            
            if not wallpapers:
                logging.warning("No popular wallpapers found")
                return None
            
            selected = wallpapers[0]
            logging.info(f"Selected popular wallpaper: {selected['id']} ({selected.get('resolution', 'unknown')})")
            
            return selected
            
        except Exception as e:
            logging.error(f"Failed to get popular wallpaper: {e}")
            return None
    
    def get_latest_wallpaper(self, search_params: Dict) -> Optional[Dict]:
        """获取最新壁纸"""
        try:
            # 设置为最新排序
            params = search_params.copy()
            params['sorting'] = 'date_added'
            
            wallpapers = self.search_wallpapers(params)
            
            if not wallpapers:
                logging.warning("No latest wallpapers found")
                return None
            
            selected = wallpapers[0]
            logging.info(f"Selected latest wallpaper: {selected['id']} ({selected.get('resolution', 'unknown')})")
            
            return selected
            
        except Exception as e:
            logging.error(f"Failed to get latest wallpaper: {e}")
            return None
    
    def test_api_connection(self) -> bool:
        """测试API连接"""
        try:
            params = {'page': 1, 'atleast': '1920x1080'}
            if self.api_key:
                params['apikey'] = self.api_key
            
            response = self.session.get(f"{self.base_url}/search", params=params)
            
            if response.status_code == 200:
                logging.info("API connection test successful")
                return True
            else:
                logging.error(f"API connection test failed: {response.status_code}")
                return False
                
        except Exception as e:
            logging.error(f"API connection test failed: {e}")
            return False
    
    @staticmethod
    def get_wallpaper_download_url(wallpaper: Dict) -> Optional[str]:
        """从壁纸信息中提取下载URL"""
        return wallpaper.get('path')
    
    @staticmethod
    def get_wallpaper_info(wallpaper: Dict) -> Dict:
        """提取壁纸关键信息"""
        return {
            'id': wallpaper.get('id'),
            'url': wallpaper.get('url'),
            'short_url': wallpaper.get('short_url'),
            'resolution': wallpaper.get('resolution'),
            'file_size': wallpaper.get('file_size'),
            'file_type': wallpaper.get('file_type'),
            'category': wallpaper.get('category'),
            'purity': wallpaper.get('purity'),
            'views': wallpaper.get('views'),
            'favorites': wallpaper.get('favorites'),
            'downloads': wallpaper.get('path'),
            'tags': [tag.get('name') for tag in wallpaper.get('tags', [])]
        }